-- Create "affiliate_transactions" table
CREATE TABLE "public"."affiliate_transactions" (
  "id" bigserial NOT NULL,
  "order_id" uuid NOT NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NULL,
  "deleted_at" timestamptz NULL,
  "transaction_type" varchar(10) NOT NULL,
  "type" varchar(20) NOT NULL,
  "chain_id" varchar(50) NOT NULL,
  "base_address" varchar(100) NOT NULL,
  "base_symbol" varchar(20) NOT NULL,
  "quote_address" varchar(100) NOT NULL,
  "quote_symbol" varchar(20) NOT NULL,
  "user_id" uuid NOT NULL,
  "user_address" varchar(100) NOT NULL,
  "base_amount" decimal(36,18) NOT NULL,
  "quote_amount" decimal(36,18) NOT NULL,
  "total_fee" decimal(36,18) NOT NULL,
  "slippage" decimal(10,6) NOT NULL,
  "status" varchar(20) NOT NULL,
  "tx_hash" varchar(100) NOT NULL,
  "mev_protect" boolean NULL DEFAULT false,
  "referrer_id" uuid NULL,
  "referral_depth" bigint NULL DEFAULT 0,
  "commission_rate" decimal(10,6) NULL DEFAULT 0,
  "commission_amount" decimal(36,18) NULL DEFAULT 0,
  "commission_paid" boolean NULL DEFAULT false,
  "commission_paid_at" timestamptz NULL,
  "volume_usd" decimal(36,18) NOT NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "uni_affiliate_transactions_order_id" UNIQUE ("order_id"),
  CONSTRAINT "uni_affiliate_transactions_tx_hash" UNIQUE ("tx_hash"),
  CONSTRAINT "fk_affiliate_transactions_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_affiliate_transactions_referrer" FOREIGN KEY ("referrer_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "chk_affiliate_transactions_transaction_type" CHECK (transaction_type = ANY (ARRAY['Buy'::text, 'Sell'::text])),
  CONSTRAINT "chk_affiliate_transactions_type" CHECK (type = ANY (ARRAY['Market'::text, 'Limit'::text, 'TPSL'::text, 'TrailingTPSL'::text])),
  CONSTRAINT "chk_affiliate_transactions_status" CHECK (status = ANY (ARRAY['pending'::text, 'completed'::text, 'failed'::text, 'cancelled'::text]))
);

-- Create indexes for affiliate_transactions table
CREATE INDEX "idx_affiliate_transactions_deleted_at" ON "public"."affiliate_transactions" ("deleted_at");
CREATE INDEX "idx_affiliate_transactions_created_at" ON "public"."affiliate_transactions" ("created_at");
CREATE INDEX "idx_affiliate_transactions_order_id" ON "public"."affiliate_transactions" ("order_id");
CREATE INDEX "idx_affiliate_transactions_transaction_type" ON "public"."affiliate_transactions" ("transaction_type");
CREATE INDEX "idx_affiliate_transactions_chain_id" ON "public"."affiliate_transactions" ("chain_id");
CREATE INDEX "idx_affiliate_transactions_base_symbol" ON "public"."affiliate_transactions" ("base_symbol");
CREATE INDEX "idx_affiliate_transactions_quote_symbol" ON "public"."affiliate_transactions" ("quote_symbol");
CREATE INDEX "idx_affiliate_transactions_user_id" ON "public"."affiliate_transactions" ("user_id");
CREATE INDEX "idx_affiliate_transactions_user_address" ON "public"."affiliate_transactions" ("user_address");
CREATE INDEX "idx_affiliate_transactions_status" ON "public"."affiliate_transactions" ("status");
CREATE INDEX "idx_affiliate_transactions_tx_hash" ON "public"."affiliate_transactions" ("tx_hash");
CREATE INDEX "idx_affiliate_transactions_referrer_id" ON "public"."affiliate_transactions" ("referrer_id");
CREATE INDEX "idx_affiliate_transactions_referral_depth" ON "public"."affiliate_transactions" ("referral_depth");
CREATE INDEX "idx_affiliate_transactions_commission_paid" ON "public"."affiliate_transactions" ("commission_paid");
CREATE INDEX "idx_affiliate_transactions_volume_usd" ON "public"."affiliate_transactions" ("volume_usd");

-- Create "sol_price_snapshots" table
CREATE TABLE "public"."sol_price_snapshots" (
  "id" bigserial NOT NULL,
  "price" decimal(36,18) NOT NULL,
  "symbol" varchar(20) NOT NULL,
  "chain_id" varchar(50) NOT NULL,
  "address" varchar(100) NOT NULL,
  "timestamp" timestamptz NOT NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id")
);

-- Create indexes for sol_price_snapshots table
CREATE INDEX "idx_sol_price_snapshots_symbol" ON "public"."sol_price_snapshots" ("symbol");
CREATE INDEX "idx_sol_price_snapshots_timestamp" ON "public"."sol_price_snapshots" ("timestamp");
CREATE INDEX "idx_sol_price_snapshots_symbol_timestamp" ON "public"."sol_price_snapshots" ("symbol", "timestamp");

-- Create composite indexes for better query performance
CREATE INDEX "idx_affiliate_transactions_user_status" ON "public"."affiliate_transactions" ("user_id", "status");
CREATE INDEX "idx_affiliate_transactions_referrer_paid" ON "public"."affiliate_transactions" ("referrer_id", "commission_paid");
CREATE INDEX "idx_affiliate_transactions_created_status" ON "public"."affiliate_transactions" ("created_at", "status");
CREATE INDEX "idx_affiliate_transactions_user_created" ON "public"."affiliate_transactions" ("user_id", "created_at");
